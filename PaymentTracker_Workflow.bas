Attribute VB_Name = "PaymentTracker_Workflow"
Option Explicit

' Define step names
Private Function StepNamesShort(stepNum As Integer) As String
    Select Case stepNum
        Case 1: StepNamesShort = "Initiate"
        Case 2: StepNamesShort = "Review"
        Case 3: StepNamesShort = "Approve"
        Case 4: StepNamesShort = "Sign"
        Case 5: StepNamesShort = "KIB"
        Case 6: StepNamesShort = "Complete"
        Case Else: StepNamesShort = "Unknown"
    End Select
End Function

' Define team members for each step
Private Function TeamMembers(stepNum As Integer) As String
    Select Case stepNum
        Case 1: TeamMembers = "<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>"
        Case 2: TeamMembers = "<PERSON><PERSON>,<PERSON>"
        Case 3: TeamMembers = "<PERSON><PERSON>,<PERSON><PERSON>,Amr"
        Case 4: TeamM<PERSON><PERSON> = "CEO with <PERSON><PERSON><PERSON>,CEO Deputy with <PERSON>neem"
        Case 5: TeamMembers = "Abdulaziz AlGharabaly,Mariam"
        Case 6: TeamMembers = "Ali (HR)"
        Case Else: TeamMembers = ""
    End Select
End Function

' Handle step clicks
Public Sub HandleStepClick(payment As PaymentData, stepNum As Integer)
    On Error Resume Next
    Dim stepStatus As String
    stepStatus = GetStepStatus(payment, stepNum)
    
    ' Only allow interaction with current step if amount is set
    If stepNum = payment.CurrentStep And payment.AmountSet Then
        Call ShowStepDialog(payment, stepNum)
    ElseIf stepStatus = "completed" Or stepStatus = "skipped" Then
        ' Show step details
        Call ShowStepDetails(payment, stepNum)
    Else
        MsgBox "This step is not yet available. Complete previous steps first.", vbInformation
    End If
End Sub

' Show step interaction dialog
Private Sub ShowStepDialog(payment As PaymentData, stepNum As Integer)
    On Error Resume Next
    Dim teamMembers As String
    teamMembers = TeamMembers(stepNum)
    
    Dim memberArray() As String
    memberArray = Split(teamMembers, ",")
    
    ' Create member selection dialog
    Dim selectedMember As String
    selectedMember = ShowMemberSelectionDialog(memberArray, stepNum)
    
    If selectedMember <> "" Then
        ' Process step completion
        Call ProcessStepCompletion(payment, stepNum, selectedMember)
    End If
End Sub

' Show member selection dialog
Private Function ShowMemberSelectionDialog(memberArray() As String, stepNum As Integer) As String
    On Error Resume Next
    Dim message As String
    message = "Select team member for " & StepNamesShort(stepNum) & ":" & vbCrLf & vbCrLf
    
    Dim i As Integer
    For i = 0 To UBound(memberArray)
        message = message & (i + 1) & ". " & memberArray(i) & vbCrLf
    Next i
    
    ' Add skip option for Step 5 (KIB)
    If stepNum = 5 Then
        message = message & vbCrLf & "S. Skip this step" & vbCrLf
    End If
    
    message = message & vbCrLf & "Enter number (or S to skip for Step 5):"
    
    Dim response As String
    response = InputBox(message, "Team Member Selection")
    
    If response = "" Then
        ShowMemberSelectionDialog = ""
        Exit Function
    End If
    
    ' Handle skip option
    If UCase(response) = "S" And stepNum = 5 Then
        ShowMemberSelectionDialog = "SKIP"
        Exit Function
    End If
    
    ' Validate numeric response
    If IsNumeric(response) Then
        Dim selection As Integer
        selection = CInt(response)
        
        If selection >= 1 And selection <= UBound(memberArray) + 1 Then
            ShowMemberSelectionDialog = memberArray(selection - 1)
        Else
            MsgBox "Invalid selection. Please try again.", vbExclamation
            ShowMemberSelectionDialog = ""
        End If
    Else
        MsgBox "Invalid input. Please enter a number.", vbExclamation
        ShowMemberSelectionDialog = ""
    End If
End Function

' Process step completion
Private Sub ProcessStepCompletion(payment As PaymentData, stepNum As Integer, selectedMember As String)
    On Error Resume Next
    If selectedMember = "SKIP" Then
        ' Skip the step
        Call SkipStep(payment, stepNum)
        MsgBox "Step " & stepNum & " (" & StepNamesShort(stepNum) & ") has been skipped.", vbInformation
    Else
        ' Complete the step
        Call AddCompletedStep(payment, stepNum)
        Call SetAssignedMember(payment, stepNum, selectedMember)
        MsgBox "Step " & stepNum & " (" & StepNamesShort(stepNum) & ") completed by " & selectedMember & "!", vbInformation
    End If
    
    ' Move to next step if not at the end
    If payment.CurrentStep < 6 Then
        payment.CurrentStep = payment.CurrentStep + 1
    End If
    
    ' Update payment data
    Call UpdatePaymentData(payment)
    
    ' Refresh display
    Call RefreshPaymentDisplay
    
    ' Check if payment is complete
    If IsPaymentComplete(payment) Then
        Dim response As VbMsgBoxResult
        response = MsgBox("Payment '" & payment.Name & "' is now complete!" & vbCrLf & vbCrLf & _
                         "Would you like to archive it?", vbYesNo + vbQuestion, "Payment Complete")
        
        If response = vbYes Then
            payment.IsArchived = True
            payment.ArchivedDate = Now
            Call UpdatePaymentData(payment)
            Call RefreshPaymentDisplay
            MsgBox "Payment archived successfully!", vbInformation
        End If
    End If
End Sub

' Show step details for completed/skipped steps
Private Sub ShowStepDetails(payment As PaymentData, stepNum As Integer)
    On Error Resume Next
    Dim stepStatus As String
    stepStatus = GetStepStatus(payment, stepNum)
    
    Dim assignedMember As String
    assignedMember = GetAssignedMember(payment, stepNum)
    
    Dim message As String
    message = "Step " & stepNum & ": " & StepNamesShort(stepNum) & vbCrLf & vbCrLf
    message = message & "Status: " & UCase(Left(stepStatus, 1)) & Mid(stepStatus, 2) & vbCrLf
    
    If stepStatus = "completed" Then
        message = message & "Amount: " & Format(payment.PaymentAmount, "0.000") & " KD" & vbCrLf
        message = message & "Completed by: " & assignedMember & vbCrLf
    ElseIf stepStatus = "skipped" Then
        message = message & "Amount: 0.000 KD" & vbCrLf
        message = message & "Reason: Skipped" & vbCrLf
    End If
    
    MsgBox message, vbInformation, "Step Details"
End Sub

' Get payment progress percentage
Public Function GetPaymentProgress(payment As PaymentData) As Double
    On Error Resume Next
    Dim completedCount As Integer
    completedCount = 0
    
    Dim i As Integer
    For i = 1 To 6
        Dim status As String
        status = GetStepStatus(payment, i)
        If status = "completed" Or status = "skipped" Then
            completedCount = completedCount + 1
        End If
    Next i
    
    GetPaymentProgress = (completedCount / 6) * 100
End Function
