Attribute VB_Name = "PaymentTracker_Events"
Option Explicit

' This code should be placed in the worksheet module for the main sheet
Public Sub Worksheet_BeforeDoubleClick(ByVal Target As Range, Cancel As Boolean)
    ' Only handle double-clicks in the payment tracking area
    If Target.Worksheet.Name = MAIN_SHEET_NAME Then
        Cancel = True ' Prevent default Excel double-click behavior
        Call HandleCellDoubleClick(Target)
    End If
End Sub

Public Sub Worksheet_SelectionChange(ByVal Target As Range)
    ' Optional: Add hover effects or selection highlighting
    ' This can be used to provide visual feedback when hovering over interactive areas
End Sub

' Quick setup procedure - call this to initialize everything
Public Sub QuickSetup()
    On Error Resume Next
    ' Initialize the payment tracker
    Call InitializePaymentTracker
    
    ' Show instructions
    MsgBox "Payment Tracker Setup Complete!" & vbCrLf & vbCrLf & _
           "Instructions:" & vbCrLf & _
           "• Double-click on Payment Info to set amount or reset payment" & vbCrLf & _
           "• Double-click on step columns to assign team members" & vbCrLf & _
           "• Use buttons to add payments and archive completed ones" & vbCrLf & _
           "• Step 5 (KIB) can be skipped if needed", vbInformation, "Setup Complete"
End Sub

' Archive management functions
Public Sub ViewArchivedPayments()
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim archivedPayments As String
    archivedPayments = "ARCHIVED PAYMENTS" & vbCrLf & "=================" & vbCrLf & vbCrLf
    
    Dim hasArchived As Boolean
    hasArchived = False
    
    Dim i As Long
    For i = 2 To ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        If ws.Cells(i, 10).Value = True Then ' IsArchived = True
            hasArchived = True
            archivedPayments = archivedPayments & ws.Cells(i, 2).Value & vbCrLf
            archivedPayments = archivedPayments & "  Amount: " & Format(ws.Cells(i, 4).Value, "0.000") & " KD" & vbCrLf
            archivedPayments = archivedPayments & "  Archived: " & Format(ws.Cells(i, 11).Value, "yyyy-mm-dd hh:mm") & vbCrLf & vbCrLf
        End If
    Next i
    
    If Not hasArchived Then
        archivedPayments = archivedPayments & "No archived payments found."
    End If
    
    MsgBox archivedPayments, vbInformation, "Archived Payments"
End Sub

' Export data functions
Public Sub ExportToCSV()
    On Error Resume Next
    Dim payments As Collection
    Set payments = GetAllActivePayments()
    
    Dim csvContent As String
    csvContent = "PaymentName,CurrentStep,PaymentAmount,AmountSet,Progress,CompletedSteps,StepStatus,AssignedMembers" & vbCrLf
    
    Dim payment As PaymentData
    Dim i As Long
    For i = 1 To payments.Count
        Set payment = payments(i)
        csvContent = csvContent & payment.Name & ","
        csvContent = csvContent & payment.CurrentStep & ","
        csvContent = csvContent & payment.PaymentAmount & ","
        csvContent = csvContent & payment.AmountSet & ","
        csvContent = csvContent & Format(GetPaymentProgress(payment), "0.0") & "%,"
        csvContent = csvContent & """" & payment.CompletedSteps & ""","
        csvContent = csvContent & """" & payment.StepStatus & ""","
        csvContent = csvContent & """" & payment.AssignedMembers & """" & vbCrLf
    Next i
    
    ' Save to file
    Dim fileName As String
    fileName = ThisWorkbook.Path & "\PaymentTracker_Export_" & Format(Now, "yyyymmdd_hhmmss") & ".csv"
    
    Dim fileNum As Integer
    fileNum = FreeFile
    
    Open fileName For Output As fileNum
    Print #fileNum, csvContent
    Close fileNum
    
    MsgBox "Data exported to: " & fileName, vbInformation, "Export Complete"
End Sub
