Attribute VB_Name = "PaymentTracker_Data"
Option Explicit

' Payment data structure (using arrays for simplicity)
Public Type PaymentData
    ID As Long
    Name As String
    CurrentStep As Integer
    PaymentAmount As Double
    AmountSet As Boolean
    CreatedDate As Date
    CompletedSteps As String  ' Comma-separated list
    StepStatus As String      ' Comma-separated list (pending,completed,skipped)
    AssignedMembers As String ' Comma-separated list
    IsArchived As Boolean
    ArchivedDate As Date
End Type

Public Const DATA_SHEET_NAME As String = "Data"
Public Const MAIN_SHEET_NAME As String = "Payment Tracker"
Public Const FIRST_PAYMENT_ROW As Integer = 2
Public Const COL_PAYMENT_INFO As Integer = 1
Public Const COL_STEP1 As Integer = 2

' Get next available payment ID
Public Function GetNextPaymentID() As Long
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    If lastRow = 1 Then
        GetNextPaymentID = 1
    Else
        GetNextPaymentID = ws.Cells(lastRow, 1).Value + 1
    End If
End Function

' Create a new payment
Public Sub CreateNewPayment(paymentName As String)
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim newID As Long
    newID = GetNextPaymentID()
    
    Dim newRow As Long
    newRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 1
    
    ' Initialize payment data
    With ws
        .Cells(newRow, 1).Value = newID                    ' PaymentID
        .Cells(newRow, 2).Value = paymentName              ' PaymentName
        .Cells(newRow, 3).Value = 1                        ' CurrentStep
        .Cells(newRow, 4).Value = 0                        ' PaymentAmount
        .Cells(newRow, 5).Value = False                    ' AmountSet
        .Cells(newRow, 6).Value = Now                      ' CreatedDate
        .Cells(newRow, 7).Value = ""                       ' CompletedSteps
        .Cells(newRow, 8).Value = "pending,pending,pending,pending,pending,pending"  ' StepStatus
        .Cells(newRow, 9).Value = ",,,,,"                  ' AssignedMembers
        .Cells(newRow, 10).Value = False                   ' IsArchived
        .Cells(newRow, 11).Value = ""                      ' ArchivedDate
    End With
End Sub

' Get payment data by ID
Public Function GetPaymentData(paymentID As Long) As PaymentData
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim payment As PaymentData
    Dim found As Boolean
    found = False
    
    Dim i As Long
    For i = 2 To ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        If ws.Cells(i, 1).Value = paymentID And Not ws.Cells(i, 10).Value Then
            With payment
                .ID = ws.Cells(i, 1).Value
                .Name = ws.Cells(i, 2).Value
                .CurrentStep = ws.Cells(i, 3).Value
                .PaymentAmount = ws.Cells(i, 4).Value
                .AmountSet = ws.Cells(i, 5).Value
                .CreatedDate = ws.Cells(i, 6).Value
                .CompletedSteps = ws.Cells(i, 7).Value
                .StepStatus = ws.Cells(i, 8).Value
                .AssignedMembers = ws.Cells(i, 9).Value
                .IsArchived = ws.Cells(i, 10).Value
                .ArchivedDate = ws.Cells(i, 11).Value
            End With
            found = True
            Exit For
        End If
    Next i
    
    GetPaymentData = payment
End Function

' Update payment data
Public Sub UpdatePaymentData(payment As PaymentData)
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim i As Long
    For i = 2 To ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        If ws.Cells(i, 1).Value = payment.ID Then
            With ws
                .Cells(i, 2).Value = payment.Name
                .Cells(i, 3).Value = payment.CurrentStep
                .Cells(i, 4).Value = payment.PaymentAmount
                .Cells(i, 5).Value = payment.AmountSet
                .Cells(i, 6).Value = payment.CreatedDate
                .Cells(i, 7).Value = payment.CompletedSteps
                .Cells(i, 8).Value = payment.StepStatus
                .Cells(i, 9).Value = payment.AssignedMembers
                .Cells(i, 10).Value = payment.IsArchived
                .Cells(i, 11).Value = payment.ArchivedDate
            End With
            Exit For
        End If
    Next i
End Sub

' Get all active payments
Public Function GetAllActivePayments() As Collection
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(DATA_SHEET_NAME)
    
    Dim payments As New Collection
    Dim payment As PaymentData
    
    Dim i As Long
    For i = 2 To ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
        If Not ws.Cells(i, 10).Value Then ' Not archived
            ' Create a new payment instance
            payment = GetEmptyPayment()
            With payment
                .ID = ws.Cells(i, 1).Value
                .Name = ws.Cells(i, 2).Value
                .CurrentStep = ws.Cells(i, 3).Value
                .PaymentAmount = ws.Cells(i, 4).Value
                .AmountSet = ws.Cells(i, 5).Value
                .CreatedDate = ws.Cells(i, 6).Value
                .CompletedSteps = ws.Cells(i, 7).Value
                .StepStatus = ws.Cells(i, 8).Value
                .AssignedMembers = ws.Cells(i, 9).Value
                .IsArchived = ws.Cells(i, 10).Value
                .ArchivedDate = ws.Cells(i, 11).Value
            End With
            payments.Add payment
        End If
    Next i
    
    Set GetAllActivePayments = payments
End Function

' Helper function to create an empty payment
Private Function GetEmptyPayment() As PaymentData
    Dim emptyPayment As PaymentData
    With emptyPayment
        .ID = 0
        .Name = ""
        .CurrentStep = 1
        .PaymentAmount = 0
        .AmountSet = False
        .CreatedDate = Now
        .CompletedSteps = ""
        .StepStatus = "pending,pending,pending,pending,pending,pending"
        .AssignedMembers = ",,,,,"
        .IsArchived = False
        .ArchivedDate = Now
    End With
    GetEmptyPayment = emptyPayment
End Function
