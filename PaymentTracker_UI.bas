Attribute VB_Name = "PaymentTracker_UI"
Option Explicit

' Refresh the payment display
Public Sub RefreshPaymentDisplay()
    On Error Resume Next
    Dim ws As Worksheet
    Set ws = Worksheets(MAIN_SHEET_NAME)
    
    ' Clear existing payment rows
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    If lastRow >= FIRST_PAYMENT_ROW Then
        ws.Range(ws.Cells(FIRST_PAYMENT_ROW, 1), ws.Cells(lastRow, 7)).Clear
    End If
    
    ' Get all active payments
    Dim payments As Collection
    Set payments = GetAllActivePayments()
    
    ' Display each payment
    Dim currentRow As Long
    currentRow = FIRST_PAYMENT_ROW
    
    Dim payment As PaymentData
    Dim i As Long
    For i = 1 To payments.Count
        Set payment = payments(i)
        Call DisplayPaymentRow(ws, payment, currentRow)
        currentRow = currentRow + 3 ' Leave space between payments
    Next i
    
    ' Update summary information
    Call UpdateSummaryInfo(ws, payments)
End Sub

' Display a single payment row
Private Sub DisplayPaymentRow(ws As Worksheet, payment As PaymentData, startRow As Long)
    On Error Resume Next
    ' Payment Info Column
    Call DisplayPaymentInfo(ws, payment, startRow)
    
    ' Step Columns
    Dim stepNum As Integer
    For stepNum = 1 To 6
        Call DisplayStepInfo(ws, payment, stepNum, startRow, COL_STEP1 + stepNum - 1)
    Next stepNum
    
    ' Add border around payment row
    With ws.Range(ws.Cells(startRow, 1), ws.Cells(startRow + 2, 7))
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
    End With
End Sub

' Display payment information in the first column
Private Sub DisplayPaymentInfo(ws As Worksheet, payment As PaymentData, startRow As Long)
    On Error Resume Next
    ' Payment name
    With ws.Cells(startRow, COL_PAYMENT_INFO)
        .Value = payment.Name
        .Font.Bold = True
        .Font.Size = 11
    End With
    
    ' Amount information
    If payment.AmountSet Then
        With ws.Cells(startRow + 1, COL_PAYMENT_INFO)
            .Value = "Amount: " & Format(payment.PaymentAmount, "0.000") & " KD"
            .Font.Size = 9
        End With
    Else
        With ws.Cells(startRow + 1, COL_PAYMENT_INFO)
            .Value = "Amount: Not Set"
            .Font.Size = 9
            .Font.Color = RGB(255, 0, 0) ' Red
        End With
    End If
    
    ' Progress information
    Dim completedCount As Integer
    completedCount = UBound(Split(payment.CompletedSteps, ",")) + 1
    If payment.CompletedSteps = "" Then completedCount = 0
    
    With ws.Cells(startRow + 2, COL_PAYMENT_INFO)
        .Value = "Progress: " & completedCount & "/6"
        .Font.Size = 9
    End With
End Sub

' Display step information
Private Sub DisplayStepInfo(ws As Worksheet, payment As PaymentData, stepNum As Integer, startRow As Long, col As Integer)
    On Error Resume Next
    Dim stepStatus As String
    stepStatus = GetStepStatus(payment, stepNum)
    
    Dim assignedMember As String
    assignedMember = GetAssignedMember(payment, stepNum)
    
    ' Step status
    With ws.Cells(startRow, col)
        Select Case stepStatus
            Case "completed"
                .Value = "✓ Completed"
                .Interior.Color = RGB(144, 238, 144) ' Light green
                .Font.Color = RGB(0, 100, 0) ' Dark green
            Case "skipped"
                .Value = "⚠ Skipped"
                .Interior.Color = RGB(255, 255, 0) ' Yellow
                .Font.Color = RGB(139, 69, 19) ' Brown
            Case "pending"
                If stepNum = payment.CurrentStep And payment.AmountSet Then
                    .Value = "→ Current"
                    .Interior.Color = RGB(173, 216, 230) ' Light blue
                    .Font.Color = RGB(0, 0, 139) ' Dark blue
                Else
                    .Value = "⏳ Waiting"
                    .Interior.Color = RGB(245, 245, 245) ' Light gray
                    .Font.Color = RGB(128, 128, 128) ' Gray
                End If
        End Select
        .Font.Size = 9
        .Font.Bold = True
    End With
    
    ' Amount information
    With ws.Cells(startRow + 1, col)
        If stepStatus = "completed" Then
            .Value = "Amount: " & Format(payment.PaymentAmount, "0.00") & " KD"
        ElseIf stepStatus = "skipped" Then
            .Value = "Amount: 0.00 KD"
        Else
            .Value = "Amount: --"
        End If
        .Font.Size = 8
    End With
    
    ' Assigned member
    With ws.Cells(startRow + 2, col)
        If assignedMember <> "" Then
            .Value = "By: " & assignedMember
        Else
            .Value = "By: --"
        End If
        .Font.Size = 8
    End With
End Sub

' Update summary information
Private Sub UpdateSummaryInfo(ws As Worksheet, payments As Collection)
    On Error Resume Next
    Dim summaryRow As Long
    summaryRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row + 3
    
    ' Active payments count
    With ws.Cells(summaryRow, 1)
        .Value = "Active Payments: " & payments.Count
        .Font.Bold = True
    End With
    
    ' Calculate total amount
    Dim totalAmount As Double
    totalAmount = 0
    
    Dim payment As PaymentData
    Dim i As Long
    For i = 1 To payments.Count
        Set payment = payments(i)
        If payment.AmountSet Then
            totalAmount = totalAmount + payment.PaymentAmount
        End If
    Next i
    
    With ws.Cells(summaryRow + 1, 1)
        .Value = "Total Amount: " & Format(totalAmount, "0.000") & " KD"
        .Font.Bold = True
    End With
End Sub
