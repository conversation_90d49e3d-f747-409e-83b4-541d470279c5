Set excel = CreateObject("Excel.Application")
excel.Visible = True

' Create a new workbook
Set wb = excel.Workbooks.Add

' Add required sheets
wb.Sheets(1).Name = "Payment Tracker"
wb.Sheets.Add
wb.Sheets(2).Name = "Data"

' Save as .xlsm
wb.SaveAs "C:\Users\<USER>\Documents\Projects\trace\PaymentTracker_App\PaymentTracker.xlsm", 52

' Initialize Data sheet headers
With wb.Sheets("Data")
    .Cells(1, 1).Value = "PaymentID"
    .Cells(1, 2).Value = "PaymentName"
    .Cells(1, 3).Value = "CurrentStep"
    .Cells(1, 4).Value = "PaymentAmount"
    .Cells(1, 5).Value = "AmountSet"
    .Cells(1, 6).Value = "CreatedDate"
    .Cells(1, 7).Value = "CompletedSteps"
    .Cells(1, 8).Value = "StepStatus"
    .Cells(1, 9).Value = "AssignedMembers"
    .Cells(1, 10).Value = "IsArchived"
    .Cells(1, 11).Value = "ArchivedDate"
    
    ' Format headers
    .Range("A1:K1").Font.Bold = True
End With

' Initialize Payment Tracker sheet
With wb.Sheets("Payment Tracker")
    .Cells(1, 1).Value = "Payment Info"
    .Cells(1, 2).Value = "Step 1"
    .Cells(1, 3).Value = "Step 2"
    .Cells(1, 4).Value = "Step 3"
    .Cells(1, 5).Value = "Step 4"
    .Cells(1, 6).Value = "Step 5"
    .Cells(1, 7).Value = "Step 6"
    
    ' Format headers
    .Range("A1:G1").Font.Bold = True
End With

wb.Save
excel.Quit
